import http from './request'
import { getExamTime, setTimeInfo } from './userInfo'
// 基础请求函数
// 获取菜单
export const getMenus = function (data = {}) {
  return http({
    url: '/c/student/platform/menu',
    method: 'GET',
    data
  })
}
// 获取code
export const getCode = function (data = {}) {
  return http({
    url: '/c/student/openid',
    method: 'GET',
    data
  })
}
// 微信小程序登录
export const Login = function (data = {}) {
  return http({
    url: '/c/student/login',
    method: 'POST',
    data
  })
}
// 验证码码登录
export const smslogin = function (data = {}) {
  return http({
    url: '/c/student/smslogin',
    method: 'POST',
    data
  })
}
export const getToken = function (data = {}) {
  return http({
    url: '/c/student/switchmerchantbrand',
    method: 'POST',
    data
  })
}

// 学习中心相关API
// 获取学习日历
export const calendar = function (data = {}) {
  return http({
    url: '/c/study/learning/calendar',
    method: 'GET',
    data
  })
}

// 获取日期课节
export const dateLessons = function (data = {}) {
  return http({
    url: '/c/study/learning/lesson',
    method: 'GET',
    data
  })
}

// 获取日期课程
export const dateCourse = function (data = {}) {
  return http({
    url: '/c/study/learning/plan',
    method: 'GET',
    data
  })
}

// 添加学习数据
export const addStudyData = function (data = {}) {
  return http({
    url: '/c/live/data/add',
    method: 'POST',
    data
  })
}

// 手机号登录获取token
export const getTokenByPhone = function (data = {}) {
  return http({
    url: '/c/student/login/appphone',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取直播地址
export const liveUrl = function (data = {}) {
  return http({
    url: '/c/live/url',
    method: 'GET',
    data
  })
}

// 获取课程详情
export const courseDetail = function (data = {}) {
  return http({
    url: '/c/study/course/detail',
    method: 'GET',
    data
  })
}

// 获取课程详情课节
export const courseDetailLessons = function (data = {}) {
  return http({
    url: '/c/study/course/detail/lessons',
    method: 'GET',
    data
  })
}

// 获取最近学习记录
export const courseDetailRecently = function (data = {}) {
  return http({
    url: '/c/study/course/detail/recently',
    method: 'GET',
    data
  })
}

// 直播/回放
export const myCourse = function (data = {}) {
	return http({
	  url: '/c/study/learning/series',
	  method: 'GET',
	  data
	})
}

// study对象，包含所有学习相关的API
export const study = {
  // 获取token
  getToken: getTokenByPhone,
  // 获取日历
  calendar: calendar,
  // 获取日期课节
  dateLessons: dateLessons,
  // 获取日期课程
  dateCourse: dateCourse,
  // 获取直播地址
  liveUrl: liveUrl,
  // 获取课程详情
  courseDetail: courseDetail,
  // 获取课程详情课节
  courseDetailLessons: courseDetailLessons,
  // 获取最近学习记录
  courseDetailRecently: courseDetailRecently,
  // 添加学习数据
  addData: addStudyData,
  myCourse:myCourse
}

// 获取题
export const getQuestionList = function (data = {}) {
  return http({
    url: '/c/tiku/question/getquestionlist',
    method: 'GET',
    data
  })
}

// 获取成绩报告
export const getScorereporting = function (data = {}) {
  return http({
    url: '/c/tiku/servicehall/scorereporting',
    method: 'GET',
    data
  })
}

// 交卷
export const postAnswer = function (data = {}) {
  return http({
    url: '/c/tiku/question/answer',
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取试卷列表
export const getPaperexercisePaperlist = function (data = {}) {
  return http({
    url: '/c/tiku/paperexercise/paperlist',
    method: 'GET',
    data
  })
}

// 我的课程测评
export const getMyCourseevaluation = function (data = {}) {
  return http({
    url: '/c/goods/v2/servicehall/mine/courseevaluation',
    method: 'GET',
    data
  })
}

// 测评分类
export const getEvaluationTypeTree = function (data = {}) {
  return http({
    url: '/c/teachingaffair/evaluation/type/tree',
    method: 'GET',
    data
  })
}

// 课程测评详情
export const getCourseevaluationDetail = function (data = {}) {
  return http({
    url: '/c/goods/v2/servicehall/mine/courseevaluation/detail',
    method: 'GET',
    data
  })
}

// 课程测评列表
export const getCourseevaluationList = function (data = {}) {
  return http({
    url: '/c/goods/v2/servicehall/mine/courseevaluation/list',
    method: 'GET',
    data
  })
}

// 课程测评 是否测评
export const getCourseIsEvaluation = function (data = {}) {
  return http({
    url: '/c/study/learning/is/evaluation',
    method: 'GET',
    data
  })
}

export const shareRecord = function (data = {}) {
  return http({
    url: '/c/goods/v2/share/record',
    method: 'POST',
    data
  })
}
export const getPhone = function (data = {}) {
  return http({
    url: '/c/student/mobile',
    method: 'GET',
    data
  })
}
// 获取专业
export const getMajor = function (data = {}) {
  return http({
    url: '/c/teaching/mapping/tree',
    method: 'GET',
    data: {
      ...data,
      is_standard: 2
    }
  })
}
// 选择专业
export const checkMajor = function (data = {}) {
  return http({
    url: '/c/student',
    method: 'PUT',
    data
  }).then(res => {
    setTimeout(() => {
      setTimeInfo({
        chapter_number: '3000'
      }).then(data => {})
    }, 1000)
    return res
  })
}
// 获取验证码
export const getSendcode = function (data = {}) {
  return http({
    url: '/b/base/sms/sendcode',
    method: 'POST',
    data
  })
}
export const index = {
  static(data) {
    // 首页统计
    return http({
      url: '/c/tiku/bistatistic/indexdata',
      method: 'GET',
      data
    })
  }
}
// 获取题目 - mock接口
export const getQuestionsDetail = data => {
  return new Promise(resolve => {
    resolve({
      msg: [''],
      code: 0,
      data: {
        section_info: [
          {
            id: '473024261183771326',
            practice_id: '479966355437655235',
            professional_id: '423160453452137587',
            chapter_id: '437657998052172130',
            knowledge_ids: ['433327977665660326'],
            knowledge_ids_name: ['权限图谱目录'],
            thematic_stem: '',
            type: '1',
            type_name: 'A1',
            level: '5',
            year: '2021',
            err_rate: '0',
            version: '1',
            parse: '<b>解析啊</b>',
            stem_list: [
              {
                id: '473024261183836862',
                sort: '1',
                content: '\u003cp\u003e是测试x\u003c/p\u003e',
                option:
                  '["<img style=' +
                  'width: 302.59px;height: 170.2px;' +
                  ' src=' +
                  'https://jinyingjiedev.oss-cn-beijing.aliyuncs.com/385164383640140890/2023/08/14/16919850760945609-1691985076094-93968.jpeg' +
                  '></img>", "\u003cp\u003e12\u003c/p\u003e你好你好你好你好你好你好《》<p>测试测试测试测试测试</p><p>《》<b>测试测试测试测试</b>测试</p>", "\u003cp\u003e13\u003c/p\u003e", "\u003cp\u003e14\u003c/p\u003e"]',
                answer: '["1","2"]',
                question_version_id: '473024261183771326'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          },
          {
            id: '473023934095168190',
            practice_id: '479966355437655235',
            professional_id: '423160453452137587',
            chapter_id: '437658088934351202',
            knowledge_ids: ['433327977665660326'],
            knowledge_ids_name: ['权限图谱目录'],
            parse: '<b>解析啊</b><p>权限图谱目录</p>',
            thematic_stem: '',
            type: '2',
            type_name: 'A2',
            level: '3',
            year: '2022',
            err_rate: '0',
            version: '1',
            stem_list: [
              {
                id: '473023934095233726',
                sort: '1',
                content: '\u003cp\u003ecs a2\u003c/p\u003e',
                option:
                  '["\u003cp\u003e1\u003c/p\u003e", "\u003cp\u003e1\u003c/p\u003e", "\u003cp\u003e1\u003c/p\u003e", "\u003cp\u003e1\u003c/p\u003e"]',
                answer: '["3"]',
                question_version_id: '473023934095168190'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          },
          {
            id: '472591768781591165',
            practice_id: '479966355437655235',
            professional_id: '423160453452137587',
            chapter_id: '437658088934351202',
            knowledge_ids: ['437658088934285700'],
            knowledge_ids_name: null,
            parse: '<p>权限图谱目录</p><b>解析啊</b>',
            thematic_stem: '',
            type: '1',
            type_name: 'A1',
            level: '1',
            year: '2021',
            err_rate: '0',
            version: '1',
            stem_list: [
              {
                id: '472591768781656701',
                sort: '1',
                content: '\u003cp\u003e题干\u003c/p\u003e',
                option:
                  '["\u003cp\u003e选项1\u003c/p\u003e", "\u003cp\u003e选项2\u003c/p\u003e", "\u003cp\u003e选项3\u003c/p\u003e", "\u003cp\u003e选项4\u003c/p\u003e"]',
                answer: '["2"]',
                question_version_id: '472591768781591165'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          },
          {
            id: '472474807628209236',
            practice_id: '479966355437655235',
            professional_id: '471847472289813815',
            chapter_id: '471852115350788211',
            knowledge_ids: ['471852156253640819'],
            knowledge_ids_name: ['知识点测试1'],
            parse: '<p>权限图谱目录</p><b>解析啊</b>',
            thematic_stem:
              '患者男，72岁。排尿困难5年，近2个月加重伴食欲缺乏。直肠指检前列腺明显增大，5cm×6cm，叩诊示膀胱已达脐下3横指。血BUN36mmol/L，Cr340μmol/L。B超示双肾中度积水。',
            type: '3',
            type_name: 'A3',
            level: '3',
            year: '2026',
            err_rate: '0',
            version: '1',
            stem_list: [
              {
                id: '472474807779204180',
                sort: '1',
                content: '下列治疗措施最为合理的是()',
                option:
                  '["经尿道前列腺切除术", "经尿道前列腺热疗", "耻骨上经膀胱前列腺切除术", "留置导尿管或耻骨上膀胱穿刺造瘘", "服用α受体拮抗剂和5α-还原酶抑制剂"]',
                answer: '["3"]',
                question_version_id: '472474807628209236'
              },
              {
                id: '472474807779269716',
                sort: '2',
                content: '良性前列腺增生症患者不宜行手术治疗的情况是()',
                option:
                  '["伴有长期、反复的下尿路感染", "伴有反复肉眼及镜下血尿", "合并腹股沟斜疝", "有急性尿潴留病史", "伴有尿道括约肌功能障碍"]',
                answer: '["4"]',
                question_version_id: '472474807628209236'
              },
              {
                id: '472474807779335252',
                sort: '3',
                content:
                  '良性前列腺增生症行经尿道前列腺切除术，下列不是术后并发症的是()',
                option:
                  '["膀胱颈瘢痕挛缩", "尿道括约肌损伤", "短暂的尿失禁现象", "尿路感染", "术后高钠血症"]',
                answer: '["4"]',
                question_version_id: '472474807628209236'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          },
          {
            id: '472474806034373716',
            practice_id: '479966355437655235',
            professional_id: '471847472289813815',
            chapter_id: '471852115350788211',
            knowledge_ids: ['471852156253640819'],
            knowledge_ids_name: ['知识点测试1'],
            parse: '<p>权限图谱目录</p><b>知识点测试1</b>',
            thematic_stem: '',
            type: '1',
            type_name: 'A1',
            level: '1',
            year: '2024',
            err_rate: '0',
            version: '1',
            stem_list: [
              {
                id: '472474806168591444',
                sort: '1',
                content: '与膀胱癌预后关系最密切的是()',
                option:
                  '["肿瘤的大小", "肿瘤的复发时间和频率", "肿瘤的数目", "肿瘤的部位", "肿瘤的病理分级和分期"]',
                answer: '["4"]',
                question_version_id: '472474806034373716'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          },
          {
            id: '472467600941524625',
            practice_id: '479966355437655235',
            professional_id: '423181984223724659',
            chapter_id: '437384173502732025',
            knowledge_ids: ['471852156253640819', '471852156253640819'],
            knowledge_ids_name: ['知识点测试1', '知识点测试1'],
            parse:
              '<p>权限图谱目录</p><b>知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1知识点测试1</b>',
            thematic_stem: '',
            type: '1',
            type_name: 'A1',
            level: '5',
            year: '2021',
            err_rate: '0',
            version: '1',
            stem_list: [
              {
                id: '472467600941590161',
                sort: '1',
                content: '\u003cp\u003e测试\u003c/p\u003e',
                option:
                  '["\u003cp\u003e测试测试测试测试测试测试测试测试测试测试测试测试测试\u003c/p\u003e", "\u003cp\u003e测试\u003c/p\u003e", "\u003cp\u003e测试\u003c/p\u003e", "\u003cp\u003e测试\u003c/p\u003e"]',
                answer: '["2"]',
                question_version_id: '472467600941524625'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          },
          {
            id: '472463707285235345',
            practice_id: '479966355437655235',
            professional_id: '423318755293007090',
            chapter_id: '471852115350788211',
            knowledge_ids: ['471852156253640800'],
            knowledge_ids_name: null,
            parse: '<span style="color:red;">471852156253640800</span>',
            thematic_stem:
              '患者男，72岁。排尿困难5年，近2个月加重伴食欲缺乏。直肠指检前列腺明显增大，5cm×6cm，叩诊示膀胱已达脐下3横指。血BUN36mmol/L，Cr340μmol/L。B超示双肾中度积水。',
            type: '3',
            type_name: 'A3',
            level: '4',
            year: '2028',
            err_rate: '0',
            version: '1',
            stem_list: [
              {
                id: '472463707285300881',
                sort: '1',
                content:
                  '\u003cp\u003e下列治疗措施最为合理的是()\u003c/p\u003e',
                option:
                  '["\u003cp\u003e经尿道前列腺切除术\u003c/p\u003e", "\u003cp\u003e经尿道前列腺切除术\u003c/p\u003e", "\u003cp\u003e耻骨上经膀胱前列腺切除术\u003c/p\u003e", "\u003cp\u003e留置导尿管或耻骨上膀胱穿刺造瘘\u003c/p\u003e", "\u003cp\u003e服用α受体拮抗剂和5α-还原酶抑制剂\u003c/p\u003e"]',
                answer: '["2"]',
                question_version_id: '472463707285235345'
              },
              {
                id: '472463707285366417',
                sort: '2',
                content:
                  '\u003cp\u003e良性前列腺增生症患者不宜行手术治疗的情况是()\u003c/p\u003e',
                option:
                  '["\u003cp\u003e伴有长期、反复的下尿路感染\u003c/p\u003e", "\u003cp\u003e伴有反复肉眼及镜下血尿\u003c/p\u003e", "\u003cp\u003e合并腹股沟斜疝\u003c/p\u003e", "\u003cp\u003e有急性尿潴留病史\u003c/p\u003e", "\u003cp\u003e伴有尿道括约肌功能障碍\u003c/p\u003e"]',
                answer: '["2"]',
                question_version_id: '472463707285235345'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          },
          {
            id: '472460051110432226',
            practice_id: '479966355437655235',
            professional_id: '422867669004327533',
            chapter_id: '471852115350788211',
            knowledge_ids: ['471852156253640819'],
            knowledge_ids_name: ['知识点测试1'],
            parse: '<span style="color:red;">471852156253640800</span>',
            thematic_stem: '',
            type: '1',
            type_name: 'A1',
            level: '5',
            year: '2027',
            err_rate: '0',
            version: '1',
            stem_list: [
              {
                id: '472460480003182050',
                sort: '1',
                content: '影响牙齿磨损的因素有()',
                option:
                  '["牙硬度", "唾液pH", "年龄的增加", "食物硬度", "人体钙摄入量"]',
                answer: '["2"]',
                question_version_id: '472460051110432226'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          },
          {
            id: '472455823587545369',
            practice_id: '479966355437655235',
            professional_id: '422867669004327533',
            chapter_id: '422907606865022163',
            knowledge_ids: ['471852156253640819'],
            knowledge_ids_name: ['知识点测试1'],
            parse: '<span style="color:red;">知识点测试1</span>',
            thematic_stem: '',
            type: '1',
            type_name: 'A1',
            level: '2',
            year: '2024',
            err_rate: '0',
            version: '1',
            stem_list: [
              {
                id: '472455908815802649',
                sort: '1',
                content:
                  '\u003cp\u003e与膀胱癌预后关系最密切的是()\u003c/p\u003e',
                option:
                  '["\u003cp\u003e肿瘤的大小\u003c/p\u003e", "\u003cp\u003e肿瘤的复发时间和频率\u003c/p\u003e", "\u003cp\u003e肿瘤的数目\u003c/p\u003e", "\u003cp\u003e肿瘤的部位\u003c/p\u003e", "\u003cp\u003e肿瘤的病理分级和分期\u003c/p\u003e"]',
                answer: '["2"]',
                question_version_id: '472455823587545369'
              }
            ],
            chapter_detail_id: '0',
            is_answer: '0',
            user_option: '',
            answer_status: '0',
            check_status: '0',
            question_statistics_info: {
              doQuestionNum: '0',
              accuracy: '0',
              errorOption: ''
            }
          }
        ]
      }
    })
  })
}

export const getstudentexaminfo = (data = {}) => {
  return http({
    url: '/c/tiku/mockexam/getstudentexaminfo',
    method: 'GET',
    data
  })
}
// 交卷
export const doSecond = data => {
  // return http.post({
  //   url: '/api/Hukao/newNextDoSecond',
  //   data: data
  // })
  return http({
    url: '/api/Hukao/newNextDoSecond',
    method: 'POST',
    data
  })
}
// 模考大赛 - 获取题
export const getjinTiKuMockExamLists = data => {
  // return http.post({
  //   url: '/MiniApps/getQuestions',
  //   data: data
  // })
  return http({
    url: '/MiniApps/getQuestions',
    method: 'POST',
    data
  })
}
// 交卷
export const submitAnswer = data => {
  // return http.post({
  //   url: '/JinTiKuNewTiKu/submitAnswer',
  //   data: data
  // })
  return http({
    url: '/JinTiKuNewTiKu/submitAnswer',
    method: 'POST',
    data
  })
}
// 获取全部考试
export const getAllExam = (data = {}) => {
  return http({
    url: '/c/tiku/mockexam/allexam',
    method: 'GET',
    data
  })
}
// 获取上次练习的信息
export const getPreInfo = (data = {}) => {
  return http({
    url: '/c/tiku/last/study/progress',
    method: 'GET',
    data
  })
}
//  出题接口
export const getquestionlist = (data = {}) => {
  return http({
    url: '/c/tiku/chapter/getquestionlist',
    method: 'GET',
    data
  })
}

//  考试交卷
export const examSubmitAnswer = data => {
  return http({
    url: '/c/tiku/question/answer',
    method: 'POST',
    data
  })
}
//  考试成绩报告
export const examScorereporting = (data = {}) => {
  return http({
    url: '/c/tiku/exam/scorereporting',
    method: 'GET',
    data
  })
}
// 查询是否是延时操作
export const getTimeOut = (data = {}) => {
  // return request.get('/c/tiku/session/delayeeventlogs', {
  //   params: data
  // })
  return http({
    url: '/c/tiku/session/delayeeventlogs',
    method: 'GET',
    data
  })
}
// 帅军接口
export const setLastId = (data = {}) => {
  return http({
    url: '/c/tiku/session/lastid',
    method: 'POST',
    header: {
      'Content-Type': 'application/json'
    },
    data
  })
}
// 查询是否有锁屏操作
export const getLock = (data = {}) => {
  return http({
    url: '/c/tiku/session/lockscreeneventlogs',
    method: 'GET',
    data
  })
}
// 是否有暂停操作
export const getPause = (data = {}) => {
  return http({
    url: '/c/tiku/session/pauseeventlogs',
    method: 'GET',
    data
  })
}
// 获取消息
export const getMessage = (data = {}) => {
  return http({
    url: '/c/tiku/session/smseventlogs',
    method: 'GET',
    data
  })
}
// 是否有强制交卷
export const getIsSubmit = (data = {}) => {
  return http({
    url: '/c/tiku/session/submiteventlogs',
    method: 'GET',
    data
  })
}
// 获取课程商品
export const getGoods = (data = {}) => {
  return http({
    url: '/c/goods/v2',
    method: 'GET',
    data
  })
}
// 获取课程商品
export const getGoodsDetail = (data = {}) => {
  return http({
    url: '/c/goods/v2/detail',
    method: 'GET',
    data
  })
}
// 获取课程商品
export const getOrderV2 = (data = {}) => {
  return http({
    url: '/c/order/v2',
    method: 'post',
    data,
    header: {
      'Content-Type': 'application/json'
    }
  })
}
export const orderList = (data = {}) => {
  return http({
    url: '/c/order/my/list',
    method: 'GET',
    data
  })
}

export const wechatapplet = (data = {}) => {
  return http({
    // url: '/c/pay/ipaynow/wechatapplet',
    url: '/c/pay/wechatpay/jsapi',
    method: 'post',
    data,
    header: {
      'Content-Type': 'application/json'
    }
  })
}

// C端首页推荐章节
export const chapterpackage = (data = {}) => {
  return http({
    url: '/c/tiku/homepage/recommend/chapterpackage',
    method: 'GET',
    data
  })
}

// 获取模考详情
export const getExaminfoDetail = (data = {}) => {
  return http({
    url: '/c/tiku/mockexam/examinfo',
    method: 'GET',
    data
  })
}
// 模考报名
export const mockexamSignup = (data = {}) => {
  return http({
    url: '/c/tiku/mockexam/signup',
    method: 'POST',
    data
  })
}
// 模考补考
export const makeupSignup = (data = {}) => {
  return http({
    url: '/c/tiku/mockexam/makeup',
    method: 'POST',
    data
  })
}

// 获取支付方式列表（新）
export const payModeListNew = function (data = {}) {
  return http({
    url: '/c/config/finance/account',
    method: 'GET',
    data
  })
}
// 支付方式详情（新）
export const payModeListNewDetail = function (data = {}) {
  return http({
    url: '/c/config/finance/account/detail',
    method: 'GET',
    data
  })
}
export const paper = function (data = {}) {
  return http({
    url: '/c/goods/v2/paper',
    method: 'GET',
    data
  })
}
export const chapterpaper = function (data = {}) {
  return http({
    url: '/c/goods/v2/chapterpaper',
    method: 'GET',
    data
  })
}
export const activateuserRecord = function (data = {}) {
  return http({
    url: '/c/student/activateuser/record',
    method: 'POST',
    data
  })
}
export const changeBasic = function (data = {}) {
  return http({
    url: '/c/student/change/basic',
    method: 'PUT',
    data
  })
}
// 活动记录
export const activityRecord = function (data = {}) {
  return http({
    url: '/c/marketing/activity/student/add',
    method: 'POST',
    data
  })
}

// 打卡接口
export const examCheckinData = function (data = {}) {
  return http({
    url: '/c/tiku/exam/checkin/data', 
    method: 'POST',
    data,
	header: {
	  'Content-Type': 'application/json'
	}
  })
}

// 打卡信息接口
export const examLearningData = function (data = {}) {
  return http({
    url: '/c/tiku/exam/learning/data',
    method: 'GET',
    data
  })
}



export const activityRaram = function (data = {}) {
  return http({
    url: '/o/base/url/param/json',
    method: 'GET',
    data
  })
}
export const miniappAuth = function (data = {}) {
  http({
    url: '/c/student/miniapp/auth',
    method: 'PUT',
    data,
    header: {
      'Content-Type': 'application/json'
    }
  })
}

export const cityRemark = function (data = {}) {
  return http({
    url: '/c/student/remark',
    method: 'PUT',
    data
  })
}
export const miniQrcode = {
  get: function (data = {}) {
    return http({
      url: '/c/marketing/wechatshare/qrcode',
      method: 'GET',
      data
    })
  },
  getQuery: function (data = {}) {
    return http({
      url: '/c/marketing/wechatshare/qrcode/decodescene',
      method: 'GET',
      data
    })
  }
}

// answer对象，包含所有答题测评相关的API
export const answer = {
  // 获取题目列表
  getQuestionList: getQuestionList,
  // 获取成绩报告
  getScorereporting: getScorereporting,
  // 提交答案
  postAnswer: postAnswer,
  // 获取试卷列表
  getPaperexercisePaperlist: getPaperexercisePaperlist,
  // 我的课程测评
  getMyCourseevaluation: getMyCourseevaluation,
  // 测评分类
  getEvaluationTypeTree: getEvaluationTypeTree,
  // 课程测评详情
  getCourseevaluationDetail: getCourseevaluationDetail,
  // 课程测评列表
  getCourseevaluationList: getCourseevaluationList,
  // 课程是否测评
  getCourseIsEvaluation: getCourseIsEvaluation
}
