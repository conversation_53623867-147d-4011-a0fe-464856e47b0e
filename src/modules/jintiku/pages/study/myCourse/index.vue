<template>
  <view class="my-course">
    <ModuleStudyMyCourse/>
  </view>
</template>
<script>
import ModuleStudyMyCourse from '@/modules/jintiku/components/study/myCourse/index.vue'
export default {
  name: "StudyMyCourse",
  components: {
    ModuleStudyMyCourse
  },
  onLoad() {
	  console.log('StudyMyCourse');
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: '我的课程'
    });
  }
}
</script>
<style lang="less" scoped>
.my-course{
  background-color: #F2F5F7;
}

</style>
