<template>
  <clientOnly>
    <div class="page-baidu" v-if="lists.length > 0">
      <!-- 顶部功能预览 -->
      <div class="priview-time" :style="{ top: statusBarHeight + 'px' }">
        <div
          class="top-head"
          :style="{ height: statusBarHeight + 20 + 'px' }"
          style="background-color: #fff"
        ></div>
        <div class="button success" @click="showModelfn">交卷</div>
        <div class="nums">
          <text>{{ current + 1 }}</text>
          <text>/{{ lists.length }}</text>
        </div>
      </div>
      <div style="height: auto; overflow: hidden">
        <div class="h96"></div>
        <div class="uni-margin-wrap">
          <ModuleAnswerExaminationQuestionSwiper
            @lists="getLists"
            @index="getCurrent"
            :lists.sync="lists"
            :index.sync="current"
            ref="exercise"
            @last="last"
          ></ModuleAnswerExaminationQuestionSwiper>
        </div>
        <div class="utils">
          <div class="gjb dtk button" @click="getSheetShow">
            <van-image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950896298723a451695089629872551_dtk.png"
              width="17px"
              height="20px"
            />
            <text style="margin-top: 8px">答题卡</text>
          </div>
          <div class="gjb dtk button" @click="onDoubtClick">
            <ClientOnly>
              <van-image
                width="17px"
                height="20px"
                :src="
                  lists[current].doubt
                    ? 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16956222038717b4a169562220387182065_%E7%BC%96%E7%BB%84%205%E5%A4%87%E4%BB%BD%402x.png'
                    : 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967375279005243169673752790025047_%E5%85%A8%E9%83%A8%E8%A7%A3%E6%9E%90%E5%A4%87%E4%BB%BD%205%402x.png'
                "
                mode="widthFix"
              />
              <text
                :class="{ doubt: lists[current].doubt }"
                style="margin-top: 8px"
              >
                {{ lists[current].doubt ? "已标疑" : "标疑" }}
              </text>
            </ClientOnly>
          </div>
          <div class="gjb pre">
            <div class="btn button flex-center" @click="prev">上一题</div>
          </div>
          <div class="gjb next">
            <div class="btn button flex-center" @click="next">下一题</div>
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="sheetShow"
      round
      closeable
      close-icon="close"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <ModuleAnswerSheet :questions="lists" @change="getCurrent" />
    </van-popup>
  </clientOnly>
</template>
<script>
const { answer, study } = useApi()
export default {
  data() {
    return {
      router: useRouter(),
      route: useRoute(),
      user: useStore.user(),
      statusBarHeight: 0,
      lists: [],
      allLists: [], // 全部
      errorStatic: false,
      total: 0,
      indicatorDots: false,
      autoplay: false,
      // 当前选定值
      current: 0,
      // 当前时间
      time: 0,
      transformTime: "",
      // 显示答题卡
      sheetShow: false,
      // 是否交卷
      isHadnPaper: false,
      // 显示授权登录按钮
      // authorizationShow: false,
      id: "",
      // type=>6 我的错题, type => 29  每日一练交卷,  type => 1 章节练习
      // type: 1,
      // mokao: false
      eid: "",
      pid: "",
      session_name: "",
      // doubt: []
      timing: "",
      informTime: "",
      mock_name: "",
      addTimeInfo: {},
      pauseInfo: {},
      type: 7, // 不同场景
      // isSubmit: false, // 是否交卷了
      stringParams: "",
      relation_id: "",
      sub_order_id: "",
      name: "",
      paper_version_id: "",
      professional_id: "",
      goods_id: "",
      order_id: "",
      evaluation_type_id: "",
      system_id: "",
      order_detail_id: "",
      lesson_id: "",
      is_reanswer: "",
    }
  },
  methods: {
    prev() {
      console.log("上一题")
      this.$refs.exercise.prev && this.$refs.exercise.prev()
    },
    next() {
      console.log("下一题")
      this.$refs.exercise.next && this.$refs.exercise.next()
    },
    last() {
      let none = this.lists.reduce((cur, next) => {
        if (next.user_option == "") {
          return cur + 1
        }
        return cur
      }, 0)
      if (!none) {
        console.log("确认交卷？")
        showConfirmDialog({
          title: "确认交卷？",
          message: `已经是最后一道题了，是否确认交卷?`,
          confirmButtonText: "确认",
          cancelButtonText: "再看看",
        })
          .then(() => {
            this.handInPapers()
          })
          .catch(() => {
            // on cancel
          })
      } else {
        showConfirmDialog({
          title: "确认交卷？",
          message: `还有${none}道题未作答，确定要交卷吗?`,
          confirmButtonText: "确认",
          cancelButtonText: "继续做题",
        })
          .then(() => {
            this.handInPapers()
          })
          .catch(() => {
            // on cancel
          })
      }
    },
    // 交卷
    handInPapers() {
      const router = useRouter()
      // if (this.isSubmit) {
      //   return
      // }
      // 交卷
      let obj = {}
      let tmpData = JSON.parse(JSON.stringify(this.lists))
      tmpData.forEach((item) => {
        if (!obj[item.question_id]) {
          obj[item.question_id] = item
          return
        }
        obj[item.question_id].stem_list.push(item.stem_list[0])
      })
      let arr = []
      for (let key in obj) {
        arr.push(obj[key])
      }
      // let cost_time = parseInt((+new Date() - this.time * 1000) / 1000)
      // let cost_time = 1
      // let product_id = 0
      // let type = 8
      let professional_id = this.professional_id
      let question_info = arr.map((item) => {
        return {
          question_id: item.question_id,
          user_option: item.stem_list.map((res) => {
            return {
              sub_question_id: res.id,
              answer: res.selected.map((jtem) => {
                return item.type == "8" || item.type == "9" || item.type == "10"
                  ? jtem.replace("\n", "<br/>")
                  : String(jtem)
              }),
            }
          }),
        }
      })

      answer
        .postAnswer({
          cost_time: 1,
          professional_id,
          type: 8,
          product_id: this.paper_version_id,
          question_info: JSON.stringify(question_info),
          order_id: this.order_id,
          sub_order_id: this.order_detail_id,
          goods_id: this.goods_id,
          user_id: "",
          goods_id: this.goods_id,
          order_id: this.order_id,
          evaluation_type_id: this.evaluation_type_id,
          teaching_system_relation_id: this.system_id,
          order_detail_id: this.order_detail_id,
        })
        .then((data) => {
          if (data?.code == 100000) {
            router.push(
              `/answertest/answerResult?goods_id=${this.goods_id}&master_order_id=${this.order_id}&paper_version_id=${this.paper_version_id}&system_id=${this.system_id}&professional_id=${this.professional_id}&order_id=${this.order_id}&evaluation_type_id=${this?.evaluation_type_id}&order_detail_id=${this?.order_detail_id}&lesson_id=${this?.lesson_id}`
            )
          } else {
            showFailToast("提交失败，请联系管理员")
          }
        })
        .catch((err) => {
          console.log("err", err)
          showFailToast(err.msg[0])
          if (err.code == 100002) {
            showFailToast(err.msg[0])
            answertestLoginLose()
          }
        })
    },
    showModelfn() {
      // 计算还有多少题没有答
      let none = this.lists.reduce((cur, next) => {
        if (next.user_option == "") {
          return cur + 1
        }
        return cur
      }, 0)
      if (!none) {
        // 直接交卷
        this.handInPapers()
      } else {
        console.log("确认交卷？")
        showConfirmDialog({
          title: "确认交卷？",
          message: `还有${none}道题未作答，确定要交卷吗?`,
          confirmButtonText: "确认",
          cancelButtonText: "继续做题",
        })
          .then(() => {
            this.handInPapers()
          })
          .catch(() => {
            // on cancel
          })
      }
    },
    success() {
      // this.$hideModel()
      this.handInPapers()
    },
    getList() {
      let data = {
        paper_version_id: this.paper_version_id,
        type: "8",
      }
      console.log("参数", data)

      if (!data?.paper_version_id || !data?.type) {
        showFailToast("缺少必要参数，请联系管理员")
        return
      }

      answer
        .getQuestionList(data)
        .then((res) => {
          this.lists = this.setQuestionLists(res.data.section_info)
        })
        .catch((err) => {
          showFailToast(err.msg[0])
          if (err.code == 100002) {
            showFailToast(err.msg[0])
            answertestLoginLose()
          }
        })

      //this.lists = this.setQuestionLists(dataData?.section_info)
    },
    // 标疑
    onDoubtClick() {
      let isDoubt = this.lists[this.current].doubt
      this.lists[this.current].doubt = !this.lists[this.current].doubt
    },
    // 处理数据
    setQuestionLists(question_list) {
      let _question_list = this.transform(question_list)
      return _question_list.map((item) => {
        return {
          ...item,
          sub_question_id: item.stem_list[0].id,
          doubt: false, // 是否为疑问题
          lookAnswer: false, // 是否查看答案了
          stem_list: item.stem_list.map((res) => {
            let option = res.option || ""
            let answer = res.answer || ""
            if (typeof option == "string") {
              try {
                option = JSON.parse(option)
              } catch (error) {
                option = []
              }
            }
            if (typeof answer == "string") {
              try {
                answer = JSON.parse(answer)
              } catch (error) {
                answer = []
              }
            }
            let resource_info = {}
            try {
              resource_info = JSON.parse(item.resource_info)
            } catch (error) {}
            return {
              ...res,
              option: option?.map((item) => {
                if (typeof item != "string") {
                  return item
                }
                // if (item.includes('<') || item.includes('>')) {
                //   return item.replace(/</g, '＜').replace(/>/g, '＞')
                // }
                return item
              }),
              answer: answer ? answer.map((item) => item * 1) : [],
              selected: (function () {
                // if (this.isFillBlanks()) {
                // if (false) {
                //   return res.selected ? res.selected : option.map(() => "")
                // }
                return res.selected ? res.selected : []
              })(), // 用户选择选项
              multiple: answer?.length > 1 ? true : false, // 是否是多选
              parse: item.parse ? item.parse : "暂无解析",
              knowledge_ids_name: item.knowledge_ids_name
                ? item.knowledge_ids_name
                : [],
              resource_info: resource_info,
            }
          }),
        }
      })
    },
    transform(question_list) {
      let _question_list = []
      for (let i = 0; i < question_list.length; i++) {
        if (question_list[i].stem_list.length == 1) {
          // _question_list.push(question_list[i])
          _question_list.push({
            ...question_list[i],
            question_id: question_list[i].id,
          })
        } else {
          for (let j = 0; j < question_list[i].stem_list.length; j++) {
            let _stem_list = question_list[i].stem_list[j]
            let question_id = question_list[i].id
            let res = {
              ...question_list[i],
              stem_list: [_stem_list],
              question_id,
            }
            _question_list.push(res)
          }
        }
      }
      return _question_list
    },
    isFillBlanks(type) {
      return type == "9"
    },
    getSheetShow() {
      this.sheetShow = true
    },
    getLists(val) {
      this.lists = val
    },
    getCurrent(val) {
      this.current = val
    },
    getUrlQuery() {
      const route = useRoute()
      if (route.query?.paper_version_id) {
        this.paper_version_id = route.query?.paper_version_id
        this.professional_id = route.query?.professional_id
        this.goods_id = route.query?.goods_id
        this.order_id = route.query?.order_id
        this.evaluation_type_id = route.query?.evaluation_type_id
        this.system_id = route.query?.system_id
        this.order_detail_id = route.query?.order_detail_id
        this.lesson_id = route.query?.lesson_id
        this.is_reanswer = route.query?.is_reanswer
        console.log("获取列表")
        if (this.is_reanswer == 1) {
          this.getList()
        } else {
          this.isEvaluation()
        }
      }
    },
    isEvaluation() {
      answer
        .getCourseIsEvaluation({
          evaluation_type_id: this.evaluation_type_id,
          lesson_id: this.lesson_id,
          order_id: this.order_id,
          paper_goods_id: this.goods_id,
          paper_version_id: this.paper_version_id,
        })
        .then((res) => {
          if (res?.data?.is_evaluation == 2) {
            this.getList()
          } else {
            // this.router.push(
            //   `/answertest/answerResult?goods_id=${this.goods_id}&master_order_id=${this.order_id}&paper_version_id=${this.paper_version_id}&system_id=${this.system_id}&professional_id=${this.professional_id}`
            // )
            const router = useRouter()
            router.replace(
              `/answertest/answerResult?goods_id=${this.goods_id}&master_order_id=${this.order_id}&paper_version_id=${this.paper_version_id}&system_id=${this.system_id}&professional_id=${this.professional_id}&order_id=${this.order_id}&evaluation_type_id=${this?.evaluation_type_id}&order_detail_id=${this?.order_detail_id}&lesson_id=${this?.lesson_id}`
            )
          }
        })
        .catch((err) => {
          console.log("err", err)
          showFailToast(err.msg[0])
          if (err.code == 100002) {
            showFailToast(err.msg[0])
            answertestLoginLose()
          }
        })
    },
  },
  watch: {
    // 监听路由对象
    $route: {
      handler(to, from) {
        console.log("to, from", to, from)
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    useHead({
      title: " ",
    })
    this.user.setFrom(this.route.query.from || "")
    if (this.route.query?.token) {
      this.user.login({
        token: this.route.query?.token?.replace(/\s+/g, ""),
      })
      this.getUrlQuery()
    } else {
      this.getUrlQuery()
    }
  },
}
</script>
<style lang="scss" scoped>
.page-baidu {
  .priview-time {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 48px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: center;
    padding: 0 20px;
    z-index: 10;
    .top-head {
      position: absolute;
      width: 100%;
      top: -58px;
      left: 0;
    }
    .nums {
      text {
        font-size: 16px;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 16px;
        color: #000000;
      }
    }
    .back {
      position: absolute;
      width: 10px;
      height: 16px;
      left: 15px;
      top: 0;
      bottom: 0;
      margin: auto 0;
    }
    .success {
      position: absolute;
      left: 20px;
      top: 0;
      bottom: 0;
      margin: auto 0;
      width: 60px;
      height: 22px;
      text-align: center;
      line-height: 22px;
      background: #01a363;
      border-radius: 4px;
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #fff;
    }
  }
  .h96 {
    height: 48px;
    background-color: #fff;
    position: relative;
    z-index: 9;
  }
  .title {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 800;
    color: #000000;
    height: 47px;
    line-height: 47px;
    text-align: center;
  }
  .time {
    height: 32px;
    line-height: 32px;
    background: #f5f8ff;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #01a363;
    text-align: center;
  }
  .uni-margin-wrap {
    width: calc(100vw);
    height: calc(100vh - 60px);
    overflow-y: scroll;
    padding-bottom: calc(40px + 21px + 18px);
    box-sizing: border-box;
  }
  .utils {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    height: 40px;
    padding-bottom: 21px;
    padding-top: 18px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 79px;
    .gjb {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      image {
        width: 17px;
        height: 20px;
        margin-bottom: 7px;
      }
      text {
        font-size: 12px;
        color: rgba(41, 65, 90, 0.75);
      }
      .btn {
        width: 96px;
        height: 40px;
        border-radius: 20px;
        border: 1px solid #01a363;
        color: #01a363;
        font-size: 13px;
      }
      .doubt {
        color: #fb9e0c;
      }
    }
    .next {
      .btn {
        border-radius: 20px;
        border: 1px solid #01a363;
        color: #fff;
        // background: linear-gradient(270deg, #6d7dff 0%, #b3a1ff 100%);
        background: #01a363;
      }
    }
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .button {
    transition: all 0.25s;
  }
}
</style>
