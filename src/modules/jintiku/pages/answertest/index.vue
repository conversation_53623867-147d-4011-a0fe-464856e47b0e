<template>
  <clientOnly>
    <div class="all-course">
      <!-- <div class="header-course">
        <div class="header-text">课程测评</div>
      </div> -->
      <div class="course-list" v-if="listData.length > 0">
        <div
          class="course-content"
          v-for="(item, index) in listData"
          :key="index"
          @click="toAnswerDetails(item)"
        >
          <div class="course-title">
            <div class="title">{{ item.goods_name }}</div>
          </div>
          <div class="subject" v-if="item.project_id_name">
            学科：{{ item.project_id_name }}
          </div>
          <div class="subject">
            <span v-if="item.goods_pname">套餐：{{ item.goods_pname }} </span>
          </div>
          <div class="segmentation"></div>
          <div>
            <div class="bottom-box">
              <div class="left-tag">
                <van-image
                  width="18"
                  height="18"
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/b606173598990778745189_%E7%BC%96%E7%BB%84%204%402x.png"
                />
              </div>
              <div class="left-title">课时检测</div>
              <div class="question-number">
                <!-- <div
                  class="noProgress"
                  v-if="
                    item.do_paper_num == item.paper_num && item.paper_num == 0
                  "
                >
                  暂无检测
                </div>
                <div
                  class="noProgress"
                  v-else-if="item.do_paper_num == item.paper_num"
                >
                  已完成
                </div> -->
                <div class="flex">
                  <div
                    class="achieve-number"
                    :class="{
                      noProgress: item.do_paper_num == 0,
                    }"
                  >
                    {{ item.do_paper_num }}
                  </div>
                  <div>/</div>
                  <div class="all-number">
                    {{ item.paper_num }}
                  </div>
                </div>
                <div class="right-img">
                  <van-image
                    width="10"
                    height="10"
                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/228e173599006726114935_%E7%BC%96%E7%BB%84%2036%402x.png"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        style="text-align: center; margin-top: 80px"
        v-if="listData.length == 0 || !listData"
      >
        <van-image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4045173295663081752515_8b3592c2dcddcac66af8ddd46abbbf1b74efa19fac63-AlBs3V_fw1200%402x.png"
          width="155px"
          height="100px"
        />
        <div class="empty-message">暂无数据</div>
      </div>
    </div>
  </clientOnly>
</template>
<script>
const { answer, study } = useApi()
export default {
  name: "all-course",
  components: {},
  data() {
    return {
      router: useRouter(),
      route: useRoute(),
      user: useStore.user(),
      already_do_paper_num: 11,
      paper_total: 12,
      listData: [],
      isLogin: true,
      majorid: "",
      citymajorName: "暂无",
      majorshow: false,
      majorTitle: "",
    }
  },
  mounted() {
    useHead({
      title: "课程测评",
    })
    this.user.setFrom(this.route.query.from || "")
    if (this.route.query.phone) {
      this.user.setPhoneCode(this.route.query.phone)
    }
    this.isLogin_status()
  },
  methods: {
    isLogin_status() {
      let self = this
      // 登录状态
      if (
        !this.user.isLogin ||
        self.user.getFrom() == "and" ||
        self.user.getFrom() == "ios"
      ) {
        let token = self.route.query.phone.replace(/\s/g, "+") + ""
        study
          .getToken({
            token: token,
          })
          .then(({ data }) => {
            this.user.login({
              ...data,
              token: data.token,
            })
            this.search()
          })
          .catch((err) => {
            console.log(err)
            showFailToast(err.msg[0])
            if (err.code == 100002) {
              showFailToast(err.msg[0])
              answertestLoginLose()
            }
          })
      } else {
        this.search()
      }
    },
    handoff() {
      this.isLogin = false
    },
    getMajorInfo() {},
    goDetection(item) {
      if (item.do_paper_num == 0 && item.paper_num == 0) {
        // showToast("")
        return
      }
    },
    toAnswerDetails(val) {
      if (val.do_paper_num == 0 && val.paper_num == 0) {
        showToast("暂无检测")
        return
      }
      const router = useRouter()
      router.push(
        `/answertest/answerDetails?goods_id=${val?.goods_id}&order_detail_id=${val?.order_detail_id}&order_id=${val?.order_id}`
      )
    },
    search() {
      console.log("查询列表")

      answer
        .getMyCourseevaluation({ noParams: true })
        .then((res) => {
          if (res?.code == 100000) {
            this.listData = res.data.courses
          } else {
            showFailToast("请求失败，请联系管理员")
          }
        })
        .catch((err) => {
          showFailToast(err.msg[0])
          if (err.code == 100002) {
            showFailToast(err.msg[0])
            answertestLoginLose()
          }
        })
    },
  },
  // watch: {
  //   // 监听路由对象
  //   $route: {
  //     handler(to, from) {
  //       this.search()
  //     },
  //     deep: true,
  //     immediate: true,
  //   },
  // },
}
</script>

<style lang="scss" scoped>
.all-course {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  padding-bottom: 16px;
  padding-top: 18px;
  background: #f2f5f7;
  font-family: PingFangSC, PingFang SC;
  .header-course {
    padding: 8px 12px;
    border-bottom: 1px solid #f2f5f7;
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    text-align: center;
    .header-text {
      font-size: 16px;
      color: #262629;
      font-weight: 500;
    }
    .uni-calendar__header-text {
      align-items: center;
    }
    .class-name {
      color: rgba(3, 32, 61, 0.75);
      font-size: 13px;
      width: 250px;
      text-align: right;
    }
    .class-bottom-img {
      width: 9px;
      height: 9px;
      margin-left: 8px;
    }
  }
  .course-list {
    width: 100%;
    padding: 12px;
    .course-content {
      width: 100%;
      // background: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/%E7%BC%96%E7%BB%84%207%402x%20(1).png");
      background-size: 100%;
      background: #ffffff;
      margin-bottom: 10px;
      border-radius: 6px;
      padding: 16px;
      padding-bottom: 14px;
      .course-title {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 12px;
        .left-tag {
          padding: 3px 8px;
          font-size: 11px;
          color: #ffffff;
          background: #2e68ff;
          border-radius: 3px;
          margin-right: 8px;
        }
        .title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 15px;
          color: #262629;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }
      .subject {
        font-size: 12px;
        color: #424b57;
        margin-top: 10px;
      }
      .segmentation {
        width: 100%;
        height: 1px;
        background: rgba(232, 233, 234, 0.5);
        margin: 12px 0;
        margin-top: 20px;
        margin-bottom: 14px;
      }
      .bottom-box {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .left-tag {
        }
        .left-title {
          font-size: 11px;
          color: #424b57;
          margin-left: 4px;
          flex-shrink: 0;
          transform: translateY(-1px);
        }
        .question-number {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          margin-left: auto;
          font-size: 13px;
          width: 100%;
          color: #424b57;
          .achieve-number {
            color: #424b57;
          }
          .noProgress {
            color: #424b57;
          }
          .all-number {
            color: rgba(66, 75, 87, 0.6);
          }
          .right-img {
            margin-left: 4px;

            image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
.empty-message {
  font-weight: 400;
  font-size: 12px;
  color: rgba(3, 32, 61, 0.45);
  margin-bottom: 32px;
}
</style>
